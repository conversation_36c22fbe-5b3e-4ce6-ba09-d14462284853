import { useEffect, useState } from 'react'
import { Button, Container, Paper, Stack, Typography } from '@mui/material'
import { Service } from '../../types/service'
import { ServiceTable } from '../../components/ResetSystem/ServiceTable'
import { useAvailableServices, useResetServices, useSeedServices } from '../../mutations/useAdminMutations'
import { convertServicesMapToList } from '../../utils/services'

const ResetSystemPage = () => {
  const { data, isLoading } = useAvailableServices()
  const resetMutation = useResetServices()
  const seedMutation = useSeedServices()
  const [services, setServices] = useState<Service[]>([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (data) {
      setServices(convertServicesMapToList(data))
    }
  }, [data])

  const updateService = (updated: Service) => {
    setServices((prev) => prev.map((s) => (s.name === updated.name ? updated : s)))
  }

  const handleSubmit = async () => {
    setLoading(true)

    const resetTargets = services.filter((s) => s.selectedReset)
    const seedTargets = services.filter((s) => s.selectedSeed)

    // 1. Reset Phase
    if (resetTargets.length > 0) {
      resetTargets.forEach((s) =>
        updateService({ ...s, status: 'pending', notes: 'Resetting...' })
      )

      try {
        const response = await resetMutation.mutateAsync(resetTargets.map((s) => s.name))
        response.results.forEach((res: any) => {
          updateService({
            ...services.find((s) => s.name === res.service)!,
            status: res.success ? 'success' : 'error',
            notes: res.message || res.error || '',
          })
        })
      } catch {
        console.log("error")
      }
    }

    // 2. Seed Phase
    if (seedTargets.length > 0) {
      seedTargets.forEach((s) =>
        updateService({ ...s, status: 'pending', notes: 'Seeding...' })
      )

      try {
        const response = await seedMutation.mutateAsync({ services: seedTargets.map((s) => s.name) })
        response.results.forEach((res: any) => {
          updateService({
            ...services.find((s) => s.name === res.service)!,
            status: res.success ? 'success' : 'error',
            notes: res.message || res.error || '',
          })
        })
      } catch {
        console.log("error")
      }
    }

    setLoading(false)
  }

  return (
    <Container maxWidth='md'>
      <Paper sx={{ p: 3, mt: 2 }}>
        <Typography variant='h3' gutterBottom>
          Reset & Seed Services
        </Typography>

        {isLoading ? (
          <Typography>Loading services...</Typography>
        ) : (
          <>
            <ServiceTable services={services} onServiceChange={updateService} />
            <Stack direction='row' spacing={2} justifyContent='flex-end' mt={2}>
              <Button variant='contained' onClick={handleSubmit} disabled={loading}>
                {loading ? 'Processing...' : 'Start'}
              </Button>
            </Stack>
          </>
        )}
      </Paper>
    </Container>
  )
}

export default ResetSystemPage
