import { FC } from 'react'
import { Routes, Route, Navigate, Outlet } from 'react-router-dom'
import ErrorPage from '../pages/ErrorPage/ErrorPage'
import DemandsPage from '../pages/DemandsPage'
import AppLayout from '../components/Layout/AppLayout'
import ProtectedRoute from './ProtectedRoute'
import DemandInstancesPage from '../pages/DemandInstancesPage'
import PipelinesPage from '../pages/PipelinesPage'
import DemandPage from '../pages/DemandPage'
import AOIsPage from '../pages/AOIsPage'
import ReportsPage from '../pages/ReportsPage'
import VesselsPage from '../pages/VesselsPage'
import EditDemandPage from '../pages/EditDemandPage'
import SingleReportPage from '../pages/SingleReportPage'
import ResetSystemPage from '../pages/ResetSystem'

const AppRoutes: FC = () => {
  return (
    <Routes>
      <Route
        path='/'
        element={
          <AppLayout>
            <Outlet />
          </AppLayout>
        }
      >
        <Route path='/' element={<Navigate to='/reports' />} />

        <Route
          path='/reports'
          element={
            <ProtectedRoute>
              <Outlet />
            </ProtectedRoute>
          }
        >
          <Route index element={<ReportsPage />} />
          <Route path=':id' element={<SingleReportPage />} />
        </Route>

        <Route
          path='/demands'
          element={
            <ProtectedRoute>
              <Outlet />
            </ProtectedRoute>
          }
        >
          <Route index element={<DemandsPage />} />
          <Route path=':id' element={<DemandPage />} />
        </Route>

        <Route
          path="/edit-demand/:id"
          element={
            <ProtectedRoute>
              <EditDemandPage />
            </ProtectedRoute>
          }
        />

        <Route
          path='/AOIs'
          element={
            <ProtectedRoute>
              <Outlet />
            </ProtectedRoute>
          }
        >
          <Route index element={<AOIsPage />} />
          <Route path=':AOIId' element={<AOIsPage />} />
        </Route>

        <Route
          path='/vessels'
          element={
            <ProtectedRoute>
              <VesselsPage />
            </ProtectedRoute>
          }
        />

        <Route
          path='/demand-instances'
          element={
            <ProtectedRoute>
              <DemandInstancesPage />
            </ProtectedRoute>
          }
        />

        <Route
          path='/pipelines'
          element={
            <ProtectedRoute>
              <PipelinesPage />
            </ProtectedRoute>
          }
        />
      </Route>

      <Route
          path='/resetSystem'
          element={
            <ProtectedRoute>
              <ResetSystemPage/>
            </ProtectedRoute>
          }
        />

      {/* Error Page & Fallback */}
      <Route path='/error' element={<ErrorPage />} />
      <Route path='*' element={<Navigate to='/error' />} />
    </Routes>
  )
}

export default AppRoutes
