import { Request, Response, NextFunction } from "express";
import * as multiServiceService from "../services/multi_service.service";
import { ApiError } from "../errors/api-error";
import logger from "../logger/logger";
import { isValidServicesArray } from "../utils/validators";

/**
 * @swagger
 * /api/v1/system/seed-services:
 *   post:
 *     summary: Seed multiple services with optional release
 *     description: |
 *       Seeds multiple services by calling their maintenance/seed endpoints.
 *       Optionally can reset services before seeding and pass a release parameter.
 *       This is similar to the existing seed functionality but handles multiple services.
 *     tags: [Multi-Service]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - services
 *             properties:
 *               services:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of service names to seed
 *                 example: ["demand", "aoi"]
 *               release:
 *                 type: string
 *                 enum:
 *                   - RELEASE1
 *                 description: Optional release parameter to pass to services
 *               resetFirst:
 *                 type: boolean
 *                 description: Whether to reset services before seeding
 *                 default: false
 *               timeout:
 *                 type: number
 *                 description: Timeout in milliseconds for each service operation
 *                 default: 30000
 *     responses:
 *       200:
 *         description: Multi-service seeding completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Multi-service seeding completed for release RELEASE1. Success: 2, Failed: 0"
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       service:
 *                         type: string
 *                         example: "demand"
 *                       operation:
 *                         type: string
 *                         example: "seed"
 *                       success:
 *                         type: boolean
 *                         example: true
 *                       message:
 *                         type: string
 *                         example: "Database seeded successfully with 5 demands and 15 instances"
 *                       data:
 *                         type: object
 *                       error:
 *                         type: string
 *                       status:
 *                         type: number
 *       400:
 *         description: Invalid request or service names
 *       500:
 *         description: Internal server error
 */
export const seedServices = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    logger.info(
      `Seed services endpoint called with request body: ${JSON.stringify(
        req.body,
        null,
        2
      )}`
    );

    const { services, release, resetFirst = false, timeout = 30000 } = req.body;

    logger.info(`Extracted parameters:`, {
      services: services,
      servicesType: typeof services,
      servicesIsArray: Array.isArray(services),
      servicesLength: Array.isArray(services) ? services.length : "N/A",
      release: release,
      resetFirst: resetFirst,
      timeout: timeout,
    });

    if (!isValidServicesArray(services)) {
      logger.error(
        `Services validation failed. Services value: ${JSON.stringify(
          services
        )}`
      );
      return next(
        new ApiError(400, "Services array is required and must not be empty")
      );
    }

    logger.info(
      `Seed services endpoint called for services: ${services.join(
        ", "
      )} with release: ${release || "default"}`
    );
    const result = await multiServiceService.seedMultipleServices(
      services,
      release,
      { resetFirst, timeout }
    );
    res.status(200).json(result);
  } catch (error: any) {
    logger.error("Error in seed services controller:", error);
    next(
      error instanceof ApiError
        ? error
        : new ApiError(500, `Error seeding services: ${error.message}`)
    );
  }
};

/**
 * @swagger
 * /api/v1/system/reset-services:
 *   post:
 *     summary: Reset multiple services
 *     description: |
 *       Resets multiple services by calling their maintenance/reset endpoints.
 *       This is similar to the existing restart functionality but handles multiple services
 *       without the full system restart process.
 *     tags: [Multi-Service]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - services
 *             properties:
 *               services:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Array of service names to reset
 *                 example: ["demand", "aoi"]
 *               timeout:
 *                 type: number
 *                 description: Timeout in milliseconds for each service operation
 *                 default: 30000
 *     responses:
 *       200:
 *         description: Multi-service reset completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Multi-service reset completed. Success: 2, Failed: 0"
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       service:
 *                         type: string
 *                         example: "aoi"
 *                       operation:
 *                         type: string
 *                         example: "reset"
 *                       success:
 *                         type: boolean
 *                         example: true
 *                       message:
 *                         type: string
 *                         example: "AOI database reset successfully, deleted 15 AOIs"
 *                       data:
 *                         type: object
 *                       error:
 *                         type: string
 *                       status:
 *                         type: number
 *       400:
 *         description: Invalid request or service names
 *       500:
 *         description: Internal server error
 */
export const resetServices = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    logger.info(
      `Reset services endpoint called with request body: ${JSON.stringify(
        req.body,
        null,
        2
      )}`
    );

    const { services, timeout = 30000 } = req.body;

    logger.info(`Extracted parameters:`, {
      services: services,
      servicesType: typeof services,
      servicesIsArray: Array.isArray(services),
      servicesLength: Array.isArray(services) ? services.length : "N/A",
      timeout: timeout,
    });

    if (!isValidServicesArray(services)) {
      logger.error(
        `Services validation failed. Services value: ${JSON.stringify(
          services
        )}`
      );
      return next(
        new ApiError(400, "Services array is required and must not be empty")
      );
    }

    logger.info(
      `Reset services endpoint called for services: ${services.join(", ")}`
    );
    const result = await multiServiceService.resetMultipleServices(services, {
      timeout,
    });
    res.status(200).json(result);
  } catch (error: any) {
    logger.error("Error in reset services controller:", error);
    next(
      error instanceof ApiError
        ? error
        : new ApiError(500, `Error resetting services: ${error.message}`)
    );
  }
};

/**
 * @swagger
 * /api/v1/system/available-services:
 *   get:
 *     summary: Get available services for multi-service operations
 *     description: Returns a list of all available services that can be used in seed-services and reset-services operations
 *     tags: [Multi-Service]
 *     responses:
 *       200:
 *         description: Available services retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               additionalProperties:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                     example: "Demand Scheduler Service"
 *                   baseUrl:
 *                     type: string
 *                     example: "http://localhost:5007"
 *                   seedEndpoint:
 *                     type: string
 *                     example: "/api/v1/scheduler/maintenance/seed"
 *                   resetEndpoint:
 *                     type: string
 *                     example: "/api/v1/scheduler/maintenance/reset"
 *                   enabled:
 *                     type: boolean
 *                     example: true
 *       500:
 *         description: Internal server error
 */
export const getAvailableServices = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    logger.info("Get available services endpoint called");
    const services = multiServiceService.getAvailableServices();
    res.status(200).json(services);
  } catch (error: any) {
    logger.error("Error in get available services controller:", error);
    next(
      new ApiError(500, `Error getting available services: ${error.message}`)
    );
  }
};
