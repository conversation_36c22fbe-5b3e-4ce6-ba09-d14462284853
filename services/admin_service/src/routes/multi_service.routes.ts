import { Router } from 'express';
import * as multiServiceController from '../controllers/multi_service.controller';

const router = Router();

/**
 * @swagger
 * tags:
 *   name: Multi-Service
 *   description: Multi-service operations for seeding and resetting multiple services simultaneously
 */

// POST /api/v1/system/seed-services
router.post('/seed-services', multiServiceController.seedServices);

// POST /api/v1/system/reset-services
router.post('/reset-services', multiServiceController.resetServices);

// GET /api/v1/system/available-services
router.get('/available-services', multiServiceController.getAvailableServices);

export default router;
