import axios from "axios";
import logger from "../logger/logger";
import { releases } from "../constants/seed/releases";
import { ApiError } from "../errors/api-error";
import { ServiceConfig, ServiceResult } from "../types/service";
import { AVAILABLE_SERVICES } from "../constants/available-services";

export const getAvailableServices = (): Record<string, ServiceConfig> => {
  return AVAILABLE_SERVICES;
};

const validateAndSplitServices = (
  serviceNames: string[],
  capability: "enableSeed" | "enableReset"
): {
  validEnabled: string[];
  validDisabled: string[];
  invalid: string[];
} => {
  return serviceNames.reduce(
    (
      acc: {
        validEnabled: string[];
        validDisabled: string[];
        invalid: string[];
      },
      name
    ) => {
      const service = AVAILABLE_SERVICES[name];
      if (!service) {
        acc.invalid.push(name);
      } else if (service[capability]) {
        acc.validEnabled.push(name);
      } else {
        acc.validDisabled.push(name);
      }
      return acc;
    },
    { validEnabled: [], validDisabled: [], invalid: [] }
  );
};

const validateServicesOrThrow = (
  validEnabled: string[],
  validDisabled: string[],
  invalid: string[],
  capability: "seed" | "reset"
) => {
  if (invalid.length > 0) {
    const msg = `Invalid service names: ${invalid.join(
      ", "
    )}. Available services: ${Object.keys(AVAILABLE_SERVICES).join(", ")}`;
    logger.error(msg);
    throw new ApiError(400, msg);
  }

  if (validDisabled.length > 0) {
    logger.warn(
      `Skipping disabled services for ${capability}: ${validDisabled.join(
        ", "
      )}`
    );
  }

  if (validEnabled.length === 0) {
    const msg = `No enabled services to ${capability}`;
    logger.error(msg);
    throw new ApiError(400, msg);
  }
};

const resetServices = async (
  serviceNames: string[],
  timeout: number
): Promise<ServiceResult[]> => {
  const resetResults = await Promise.allSettled(
    serviceNames.map(async (serviceName) => {
      const service = AVAILABLE_SERVICES[serviceName];
      const resetUrl = `${service.baseUrl}${service.resetEndpoint}`;

      logger.info(`Resetting service: ${service.name}`, { resetUrl });
      try {
        const response = await axios.post(resetUrl, {}, { timeout });
        return {
          service: serviceName,
          operation: "reset",
          success: true,
          message: response.data.message || "Reset successful",
          data: response.data,
        };
      } catch (error: any) {
        logger.error(`Failed to reset ${service.name}`, error);
        return {
          service: serviceName,
          operation: "reset",
          success: false,
          error: error.message,
          status: error.response?.status,
        };
      }
    })
  );

  return resetResults.map((r) =>
    r.status === "fulfilled" ? r.value : r.reason
  );
};

export const seedMultipleServices = async (
  serviceNames: string[],
  release?: releases,
  options: { timeout?: number; resetFirst?: boolean } = {}
): Promise<{ message: string; results: ServiceResult[] }> => {
  try {
    const { timeout = 30000, resetFirst = false } = options;
    logger.info(`Starting multi-service seeding`, {
      serviceNames,
      release,
      timeout,
      resetFirst,
    });

    const { validEnabled, validDisabled, invalid } = validateAndSplitServices(
      serviceNames,
      "enableSeed"
    );

    validateServicesOrThrow(validEnabled, validDisabled, invalid, "seed");

    const results: ServiceResult[] = [];

    if (resetFirst) {
      logger.info("Resetting services before seeding...");
      const resetResults = await resetServices(validEnabled, timeout);
      results.push(...resetResults);
    }

    logger.info("Seeding services...");
    const seedResults = await Promise.allSettled(
      validEnabled.map(async (serviceName) => {
        const service = AVAILABLE_SERVICES[serviceName];
        const seedUrl = `${service.baseUrl}${service.seedEndpoint}`;

        logger.info(`Seeding service: ${service.name}`, { seedUrl });
        try {
          const requestBody = release ? { release } : {};
          const response = await axios.post(seedUrl, requestBody, { timeout });
          return {
            service: serviceName,
            operation: "seed",
            success: true,
            message: response.data.message || "Seed successful",
            data: response.data,
          };
        } catch (error: any) {
          logger.error(`Failed to seed ${service.name}`, error);
          return {
            service: serviceName,
            operation: "seed",
            success: false,
            error: error.message,
            status: error.response?.status,
          };
        }
      })
    );

    results.push(
      ...seedResults.map((r) => (r.status === "fulfilled" ? r.value : r.reason))
    );

    const successCount = results.filter((r) => r.success).length;
    const failCount = results.filter((r) => !r.success).length;
    const summary = `Multi-service seeding completed for release ${
      release || "default"
    }. Success: ${successCount}, Failed: ${failCount}`;
    logger.info(summary);

    return { message: summary, results };
  } catch (error) {
    logger.error("Error during multi-service seeding", error);
    throw error;
  }
};

export const resetMultipleServices = async (
  serviceNames: string[],
  options: { timeout?: number } = {}
): Promise<{ message: string; results: ServiceResult[] }> => {
  try {
    const { timeout = 30000 } = options;
    logger.info(`Starting multi-service reset`, { serviceNames, timeout });

    const { validEnabled, validDisabled, invalid } = validateAndSplitServices(
      serviceNames,
      "enableReset"
    );

    validateServicesOrThrow(validEnabled, validDisabled, invalid, "reset");

    const results = await resetServices(validEnabled, timeout);
    const successCount = results.filter((r) => r.success).length;
    const failCount = results.filter((r) => !r.success).length;
    const summary = `Multi-service reset completed. Success: ${successCount}, Failed: ${failCount}`;
    logger.info(summary);

    return { message: summary, results };
  } catch (error) {
    logger.error("Error during multi-service reset", error);
    throw error;
  }
};
