let SEED_RUNNING = false;
let RESET_RUNNING = false;

let LAST_SEED_RUN: Date | null = null;
let LAST_RESET_RUN: Date | null = null;

const SEED_STATS = {
  total_runs: 0,
  total_aois_created: 0,
};

const RESET_STATS = {
  total_runs: 0,
  total_aois_deleted: 0,
};

const startOperation = (type: 'seed' | 'reset') => {
  if (type === 'seed') SEED_RUNNING = true;
  else RESET_RUNNING = true;
};

const endOperation = (type: 'seed' | 'reset') => {
  if (type === 'seed') SEED_RUNNING = false;
  else RESET_RUNNING = false;
};

const updateStats = (type: 'seed' | 'reset', count: number) => {
  const now = new Date();
  if (type === 'seed') {
    SEED_STATS.total_runs += 1;
    SEED_STATS.total_aois_created += count;
    LAST_SEED_RUN = now;
  } else {
    RESET_STATS.total_runs += 1;
    RESET_STATS.total_aois_deleted += count;
    LAST_RESET_RUN = now;
  }
};

const generateMultipleSampleAois = (count: number) =>
  Array.from({ length: count }, (_, index) => generateSampleAoiData(index));
/**
 * Generate sample AOI data for seeding
 */
const generateSampleAoiData = (index: number) => {
  const baseNames = [
    'Mediterranean Sea',
    'Red Sea',
    'Persian Gulf',
    'Black Sea',
    'Baltic Sea',
    'North Sea',
    'Adriatic Sea',
    'Aegean Sea',
    'Caspian Sea',
    'Sea of Azov'
  ];

  const baseLat = 35.0 + (index * 2.5);
  const baseLon = 15.0 + (index * 3.0);
  
  return {
    name: `${baseNames[index % baseNames.length]} AOI ${index + 1}`,
    data: {
      type: "FeatureCollection" as const,
      features: [
        {
          type: "Feature" as const,
          properties: {
            name: `${baseNames[index % baseNames.length]} Feature`,
            priority: Math.floor(Math.random() * 5) + 1,
            area_km2: Math.floor(Math.random() * 10000) + 1000
          },
          geometry: {
            type: "Polygon" as const,
            coordinates: [[
              [baseLon, baseLat],
              [baseLon + 1, baseLat],
              [baseLon + 1, baseLat + 1],
              [baseLon, baseLat + 1],
              [baseLon, baseLat]
            ]]
          }
        }
      ]
    }
  };
};

const simulateServiceAvailability = (): boolean => {
  return Math.random() < 0.8;
};

export const isSeeding = (): boolean => SEED_RUNNING;
export const isResetting = (): boolean => RESET_RUNNING;

const getStatusInfo = (
  type: 'seed' | 'reset',
  isRunning: boolean,
  lastRun: Date | null,
  stats: Record<string, any>,
  defaultAgoMs: number,
  frequencyIfRunning: number
) => {
  const currentTime = new Date();
  const last = lastRun || new Date(currentTime.getTime() - defaultAgoMs);
  const next = isRunning ? new Date(currentTime.getTime() + frequencyIfRunning * 1000) : null;

  return {
    is_running: isRunning,
    last_run: last.toISOString(),
    next_run: next?.toISOString() ?? null,
    frequency: isRunning ? frequencyIfRunning : null,
    stats: { ...stats }
  };
};

/**
 * Seed the database with initial AOI data for testing and development
 */
export const seedDatabaseLogic = async (): Promise<{ message: string }> => {
  startOperation('seed');
  try {
    console.log('Starting AOI database seeding operation');

    await new Promise(resolve => setTimeout(resolve, 6000));

    if (!simulateServiceAvailability()) {
      const error = new Error('Seed service not found or unavailable');
      (error as any).statusCode = 404;
      throw error;
    }

    const numAoisToCreate = Math.floor(Math.random() * 8) + 3;
    const sampleAois = generateMultipleSampleAois(numAoisToCreate);
    
    // Optional: await AoiModel.insertMany(sampleAois);

    updateStats('seed', numAoisToCreate);

    return {
      message: `AOI database seeded successfully with ${numAoisToCreate} AOIs`,
    };
  } catch (error: any) {
    console.error(`Error seeding AOI database: ${error.message}`);
    throw new Error(`Failed to seed AOI database: ${error.message}`);
  } finally {
    endOperation('seed');
  }
};

/**
 * Reset the database by removing all AOIs
 */
export const resetDatabaseLogic = async (): Promise<{ message: string }> => {
  startOperation('reset');
  try {
    console.log('Starting AOI database reset operation');

    // Simulate some async operation
    await new Promise(resolve => setTimeout(resolve, 1000));

    if (!simulateServiceAvailability()) {
      const error = new Error('Reset service not found or unavailable');
      (error as any).statusCode = 404;
      throw error;
    }

    const deletedCount = 0; // In real logic, calculate deleted AOIs
    // Optional: await AoiModel.deleteMany({});

    updateStats('reset', deletedCount);

    return {
      message: `AOI database reset successfully, deleted ${deletedCount} AOIs`,
    };
  } catch (error: any) {
    if (error.statusCode === 404) {
      throw error;
    }
    console.error(`Error resetting AOI database: ${error.message}`);
    throw new Error(`Failed to reset AOI database: ${error.message}`);
  } finally {
    endOperation('reset');
  }
};


/**
 * Get information about the current seeding process
 */
export const getSeedStatusInfo = () => {
  return getStatusInfo('seed', isSeeding(), LAST_SEED_RUN, SEED_STATS, 60 * 60 * 1000, 300);
};

/**
 * Get information about the current reset process
 */
export const getResetStatusInfo = () => {
  return getStatusInfo('reset', isResetting(), LAST_RESET_RUN, RESET_STATS, 3 * 60 * 60 * 1000, 600);
};
