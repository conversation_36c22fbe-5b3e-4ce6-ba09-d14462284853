from fastapi import APIRouter, HTTPException, status
from services.maintenance_service import (
    seed_database_logic, 
    reset_database_logic, 
    get_seed_status_info, 
    get_reset_status_info
)
from typing import Dict, Any

maintenance_router = APIRouter()

@maintenance_router.post(
    "/seed",
    response_model=Dict[str, str],
    status_code=status.HTTP_200_OK,
    summary="Seed the database with initial data",
    description="""
    Initialize the database with seed data for testing and development purposes.
    
    This endpoint populates the database with predefined instances and demands.
    It can be used to reset the system to a known state for testing.
    """,
    responses={
        200: {
            "description": "Database seeded successfully",
            "content": {
                "application/json": {
                    "example": {"message": "Database seeded successfully"}
                }
            }
        },
        500: {
            "description": "Error seeding database",
            "content": {
                "application/json": {
                    "example": {"detail": "Failed to seed database: [error details]"}
                }
            }
        }
    }
)
async def seed_database():
    """
    Seed the database with initial data for testing and development.
    
    Returns a success message if the operation completes successfully.
    """
    return await seed_database_logic()

@maintenance_router.post(
    "/reset",
    response_model=Dict[str, str],
    status_code=status.HTTP_200_OK,
    summary="Reset the database",
    description="""
    Reset the database by removing all instances and demands.
    
    This endpoint clears all data from the database, returning it to an empty state.
    Use with caution as this operation cannot be undone.
    """,
    responses={
        200: {
            "description": "Database reset successfully",
            "content": {
                "application/json": {
                    "example": {"message": "Database reset successfully"}
                }
            }
        },
        404: {
            "description": "Reset service not found",
            "content": {
                "application/json": {
                    "example": {"detail": "Reset service not found or unavailable"}
                }
            }
        },
        500: {
            "description": "Error resetting database",
            "content": {
                "application/json": {
                    "example": {"detail": "Failed to reset database: [error details]"}
                }
            }
        }
    }
)
async def reset_database():
    """
    Reset the database by removing all instances and demands.
    
    Returns a success message if the operation completes successfully.
    """
    return await reset_database_logic()

@maintenance_router.get(
    "/seed",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Get seeding status and information",
    description="""
    Get information about the current seeding process.
    
    This endpoint returns whether seeding is running and additional information
    about the seeding process.
    """,
    responses={
        200: {
            "description": "Seeding status retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "is_running": True,
                        "last_run": "2023-06-15T14:30:22Z",
                        "next_run": "2023-06-15T14:35:22Z",
                        "frequency": 300,
                        "stats": {
                            "total_runs": 5,
                            "total_demands_created": 25,
                            "total_instances_created": 120
                        }
                    }
                }
            }
        }
    }
)
async def get_seed_status():
    """
    Get information about the current seeding process.
    
    Returns:
        Dict with seeding status and information
    """
    return await get_seed_status_info()

@maintenance_router.get(
    "/reset",
    response_model=Dict[str, Any],
    status_code=status.HTTP_200_OK,
    summary="Get reset status and information",
    description="""
    Get information about the current reset process.
    
    This endpoint returns whether reset is running and additional information
    about the reset process.
    """,
    responses={
        200: {
            "description": "Reset status retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "is_running": False,
                        "last_run": "2023-06-15T12:00:00Z",
                        "next_run": None,
                        "frequency": None,
                        "stats": {
                            "total_runs": 2,
                            "total_demands_deleted": 15,
                            "total_instances_deleted": 75
                        }
                    }
                }
            }
        }
    }
)
async def get_reset_status():
    """
    Get information about the current reset process.
    
    Returns:
        Dict with reset status and information
    """
    return await get_reset_status_info()
