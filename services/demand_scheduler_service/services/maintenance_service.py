import asyncio
from datetime import datetime, timedelta
import random
from fastapi import HTTP<PERSON>x<PERSON>
import logging

# Setup logging
logger = logging.getLogger(__name__)

# Global status variables
SEED_RUNNING = False
RESET_RUNNING = False

# Last run timestamps
LAST_SEED_RUN = None
LAST_RESET_RUN = None

# Statistics counters
SEED_STATS = {
    "total_runs": 0,
    "total_demands_created": 0,
    "total_instances_created": 0,
}

RESET_STATS = {
    "total_runs": 0,
    "total_demands_deleted": 0,
    "total_instances_deleted": 0,
}

def is_seeding():
    """Check if seeding process is currently running."""
    global SEED_RUNNING
    return SEED_RUNNING

def is_resetting():
    """Check if reset process is currently running."""
    global RESET_RUNNING
    return RESET_RUNNING

async def seed_database_logic():
    """
    Seed the database with initial data for testing and development.
    
    Returns a success message if the operation completes successfully.
    """
    global SEED_RUNNING, LAST_SEED_RUN, SEED_STATS
    
    try:
        logger.info("Starting database seeding operation")
        SEED_RUNNING = True
        
        # Simulate work
        await asyncio.sleep(3)
        
        # Update statistics
        demands_created = random.randint(3, 10)
        instances_created = random.randint(10, 30)
        
        SEED_STATS["total_runs"] += 1
        SEED_STATS["total_demands_created"] += demands_created
        SEED_STATS["total_instances_created"] += instances_created
        
        # Update last run timestamp
        LAST_SEED_RUN = datetime.utcnow()
        
        logger.info(f"Database seeded successfully with {demands_created} demands and {instances_created} instances")
        return {"message": f"Database seeded successfully with {demands_created} demands and {instances_created} instances"}
    
    except Exception as e:
        logger.error(f"Error seeding database: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to seed database: {str(e)}")
    
    finally:
        SEED_RUNNING = False

async def reset_database_logic():
    """
    Reset the database by removing all instances and demands.
    
    Returns a success message if the operation completes successfully.
    """
    global RESET_RUNNING, LAST_RESET_RUN, RESET_STATS
    
    try:
        logger.info("Starting database reset operation")
        RESET_RUNNING = True
        
        # Simulate service check
        await asyncio.sleep(2)
        
        # Check if service is available (simulated)
        service_available = random.choice([True, False])
        
        if not service_available:
            logger.warning("Reset service not found or unavailable")
            raise HTTPException(
                status_code=404,
                detail="Reset service not found or unavailable"
            )
        
        # TODO: Implement actual reset logic here
        # For example:
        # - Delete all demands
        # - Delete all instances
        
        # Simulate work
        await asyncio.sleep(3)
        
        # Update statistics
        demands_deleted = random.randint(5, 20)
        instances_deleted = random.randint(20, 100)
        
        RESET_STATS["total_runs"] += 1
        RESET_STATS["total_demands_deleted"] += demands_deleted
        RESET_STATS["total_instances_deleted"] += instances_deleted
        
        # Update last run timestamp
        LAST_RESET_RUN = datetime.utcnow()
        
        logger.info(f"Database reset successfully, deleted {demands_deleted} demands and {instances_deleted} instances")
        return {"message": f"Database reset successfully, deleted {demands_deleted} demands and {instances_deleted} instances"}
    
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    
    except Exception as e:
        logger.error(f"Error resetting database: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to reset database: {str(e)}")
    
    finally:
        RESET_RUNNING = False

async def get_seed_status_info():
    """
    Get information about the current seeding process.
    
    Returns:
        Dict with seeding status and information
    """
    global SEED_RUNNING, LAST_SEED_RUN, SEED_STATS
    
    current_time = datetime.utcnow()
    is_running = is_seeding()
    
    # Get last run time or default
    last_run = LAST_SEED_RUN or (current_time - timedelta(minutes=60))
    
    # Calculate next run time (for demonstration purposes)
    next_run = current_time + timedelta(minutes=5) if is_running else None
    
    # Set frequency (for demonstration purposes)
    frequency = 300 if is_running else None
    
    return {
        "is_running": is_running,
        "last_run": last_run.isoformat() + "Z",
        "next_run": next_run.isoformat() + "Z" if next_run else None,
        "frequency": frequency,
        "stats": SEED_STATS.copy()
    }

async def get_reset_status_info():
    """
    Get information about the current reset process.
    
    Returns:
        Dict with reset status and information
    """
    global RESET_RUNNING, LAST_RESET_RUN, RESET_STATS
    
    current_time = datetime.utcnow()
    is_running = is_resetting()
    
    # Get last run time or default
    last_run = LAST_RESET_RUN or (current_time - timedelta(minutes=180))
    
    # Calculate next run time (for demonstration purposes)
    next_run = current_time + timedelta(minutes=10) if is_running else None
    
    # Set frequency (for demonstration purposes)
    frequency = 600 if is_running else None
    
    return {
        "is_running": is_running,
        "last_run": last_run.isoformat() + "Z",
        "next_run": next_run.isoformat() + "Z" if next_run else None,
        "frequency": frequency,
        "stats": RESET_STATS.copy()
    }